import { BaseApiObject } from '../base';

/**
 * Interface for game events.
 */
export interface GameHistorySpin extends BaseApiObject {
  roundId?: number | string;
  spinNumber: number;
  type?: string;
  gameVersion?: string;
  endOfRound: boolean;
  walletTransactionId?: string;
  win: number;
  bet: number;
  balanceBefore: number;
  balanceAfter: number;
  freeBetCoin?: number;
  ts: string;
  result?: string;
  userAgentId?: number;
  test: boolean;
  extraData?: {
    regulatoryData: {
      aamsSessionCode: string;
      ropCode: string;
      participationStartDate: string;
    }
  };
}

export const keysToRemoveForPureJSON = [
  '_meta', 'balanceAfter', 'balanceBefore', 'bet', 'currency', 'endOfRound', 'freeSpinNumber', 'isFree',
  'isPayment', 'test', 'type', 'win', 'hasPrev', 'prevType', 'hasNext', 'nextType'
];
