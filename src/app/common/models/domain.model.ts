import { BaseApiObject } from '../typings';

export type DomainType = 'static' | 'dynamic' | 'lobby';
export const DOMAIN_TYPES: { 'static': DomainType, 'dynamic': DomainType, 'lobby': DomainType } = {
  'static': 'static',
  'dynamic': 'dynamic',
  'lobby': 'lobby',
};

export interface DomainPoolItem {
  id: string;
  isActive?: boolean;
}

export interface DomainPoolRow {
  id: string;
  inherited?: boolean;
  name?: string;
  domains?: DomainPoolItem[];
  lobbyDomains?: DomainPoolItem[];
  createdAt: string;
  updatedAt: string;
}

export type DomainPool = DomainPoolRow & BaseApiObject;

export interface DomainRow {
  id: string;
  name?: string;
  domain: string;
  environment: string;
  isActive?: boolean;
  createdAt: string;
  updatedAt: string;
}

export type Domain = DomainRow & BaseApiObject;

export interface DomainsItemDialogData {
  domain?: Domain;
  gameServers?: string[];
  type: DomainType;
}
