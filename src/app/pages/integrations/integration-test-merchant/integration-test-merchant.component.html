<div class="body-test">
  <div class="margin-right24 margin-left24">
    <h3>
      {{ 'INTEGRATIONS.seamless' | translate }}
    </h3>
    <div class="margin24">
      <form [formGroup]="form">
        <h5>
          {{ 'INTEGRATIONS.merchantSettings' | translate }}:
        </h5>
        <div class="merchant-settings">
          <mat-form-field appearance="outline" class="width100">
            <mat-label>{{'INTEGRATIONS.merchantCode' | translate}}</mat-label>
            <input matInput trimValue formControlName="code">
          </mat-form-field>
          <mat-form-field appearance="outline" class="width100">
            <mat-label>{{'INTEGRATIONS.merchantType' | translate}}</mat-label>
            <input matInput trimValue formControlName="type">
          </mat-form-field>
          <div formGroupName="params">
            <mat-form-field appearance="outline" class="width100">
              <mat-label>{{'INTEGRATIONS.merchantPassword' | translate}}</mat-label>
              <input matInput trimValue formControlName="password" type="password">
            </mat-form-field>
            <mat-form-field appearance="outline" class="width100">
              <mat-label>{{'INTEGRATIONS.serverURL' | translate}}</mat-label>
              <input matInput trimValue formControlName="serverUrl">
            </mat-form-field>
          </div>
        </div>
        <h5>
          {{ 'INTEGRATIONS.testExecutionParameters' | translate }}:
        </h5>
        <div class="merchant-settings">

          <mat-form-field appearance="outline" class="width100">
            <mat-label>{{'INTEGRATIONS.gameCode' | translate}}</mat-label>
            <lib-swui-select
              [title]="'INTEGRATIONS.gameCode' | translate"
              [placeholder]="'INTEGRATIONS.gameCode' | translate"
              [showSearch]="true"
              [data]="games"
              formControlName="gameCode">
            </lib-swui-select>
          </mat-form-field>

          <mat-form-field appearance="outline" class="width100">
            <mat-label>{{'INTEGRATIONS.secondGameCode' | translate}}</mat-label>
            <lib-swui-select
              [title]="'INTEGRATIONS.secondGameCode' | translate"
              [placeholder]="'INTEGRATIONS.secondGameCode' | translate"
              [showSearch]="true"
              [data]="games"
              formControlName="secondGameCode">
            </lib-swui-select>
          </mat-form-field>

          <mat-form-field appearance="outline" class="width100">
            <mat-label>{{'INTEGRATIONS.customId' | translate}}</mat-label>
            <input matInput trimValue formControlName="custId">
          </mat-form-field>

          <mat-form-field appearance="outline" class="width100">
            <mat-label>{{'INTEGRATIONS.currencyCode' | translate}}</mat-label>
            <lib-swui-select
              [title]="'INTEGRATIONS.currencyCode' | translate"
              [placeholder]="'INTEGRATIONS.currencyCode' | translate"
              [showSearch]="true"
              [data]="currencies"
              formControlName="currencyCode">
            </lib-swui-select>
          </mat-form-field>

          <mat-form-field appearance="outline" class="width100">
            <mat-label>{{'INTEGRATIONS.ticket' | translate}}</mat-label>
            <input matInput trimValue formControlName="ticket">
            <mat-icon
              matSuffix
              matTooltip="{{'INTEGRATIONS.ticketRequiredCase' | translate}}"
              class="help-icon">
              help
            </mat-icon>
          </mat-form-field>

          <div formGroupName="specialCases">
            <div class="width100 margin-bottom16">
              <mat-checkbox formControlName="isNeedJPTests">
                Need JP Tests
              </mat-checkbox>
            </div>

            <div class="width100 margin-bottom16">
              <mat-checkbox formControlName="isNeedFreeBetTests">
                Need Free Bet Tests
              </mat-checkbox>
            </div>

            <div class="width100 margin-bottom16">
              <mat-checkbox formControlName="isNeedMainTests">
                Need Main Tests
              </mat-checkbox>
            </div>
            <div class="width100 margin-bottom16">
              <mat-checkbox formControlName="isNeedBonusAPITests">
                Need Bonus API Tests
              </mat-checkbox>
            </div>
            <div class="width100 margin-bottom16">
              <mat-checkbox formControlName="isNeedMultiSessionTests">
                Need Multi Session Tests
              </mat-checkbox>
            </div>
            <div class="width100 margin-bottom16">
              <mat-checkbox formControlName="isNeedCrossGameMultiSessionTests">
                Need Cross Game Multi Session Tests
              </mat-checkbox>
            </div>
            <div class="width100 margin-bottom16">
              <mat-checkbox formControlName="isNeedMultibetRollbackTests">
                Need Multibet Rollback Tests
              </mat-checkbox>
            </div>
            <div class="width100 margin-bottom16">
              <mat-checkbox formControlName="shouldValidateTicketOnlyOnce">
                Should Validate Ticket Only Once
              </mat-checkbox>
            </div>
            <div class="width100 margin-bottom16">
              <mat-checkbox formControlName="isNeedFinalizationTests">
                Need Finalization Tests
              </mat-checkbox>
            </div>
          </div>
        </div>
        <div class="custom-error custom-error--info margin-bottom16">
          <div fxLayout="row">
            <div>
              <a target="_blank" routerLink="/gitbook/skywind-seamless-api-documentation">{{'INTEGRATIONS.seamlessDoc' | translate}}</a>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

<div fxLayout="row" fxLayoutAlign="end center">
  <button mat-button (click)="handleSaveAction()">
    {{'INTEGRATIONS.btnSave' | translate}}
  </button>
  <button mat-flat-button color="primary"
          [disabled]="loading"
          (click)="runTest()">
    <div fxLayout="row" fxLayoutAlign="center center">
      <mat-spinner *ngIf="loading"
                   class="margin-right4"
                   diameter="20"
                   color="accent">
      </mat-spinner>
      {{'INTEGRATIONS.btnTest' | translate}}
    </div>
  </button>
</div>
