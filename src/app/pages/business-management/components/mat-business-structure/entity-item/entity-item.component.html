<td [ngClass]="{'disabled-cell': isEntityRowDisabled}">
  <div class="name-wrapper">
    <div class="bs-entity" [style.padding-left]="(entity.level + 1) * 20 + 'px'">
      <div class="bs-entity__arrow">
        <mat-icon *ngIf="entity.children.length > 0" (click)="onItemClick(entity)">
          {{ expanded ? 'expand_more' : 'chevron_right' }}
        </mat-icon>
      </div>

      <ng-container [ngSwitch]="entity.type">
        <span class="bs-entity__type bs-type bs-type--entity" *ngSwitchCase="'entity'" title="Reseller">R</span>
        <span class="bs-entity__type bs-type bs-type--brand" *ngSwitchCase="'brand'" title="Operator">O</span>
        <span class="bs-entity__type bs-type bs-type--merchant" *ngSwitchCase="'merchant'" title="Merchant">O</span>
        <span class="bs-entity__type bs-type bs-type--studio" *ngSwitchCase="'liveStudio'" title="Live Studio">S</span>
      </ng-container>

      <span class="bs-entity__title" [style]="{width: (bsService.maxLength - (entity.level * 20)) + 'px'}">
        {{ entity.title || entity.name }}
        <span *ngIf="entity.isTest" class="sw-chip" [ngClass]="'sw-chip-default'">Test</span>
        <ng-container *ngIf="!isEntitySuspended">
        <button
          mat-icon-button
          *ngIf="isEntityEditAllowed(entity)"
          class="bs-entity__edit"
          (click)="handleOpenEdit($event)">
        <mat-icon fontSet="material-icons-outline">edit</mat-icon>
      </button>
        </ng-container>
      </span>

      <div class="bs-entity__spinner" *ngIf="merchantLoading || entityUpdate">
        <mat-progress-spinner diameter="14" mode="indeterminate"></mat-progress-spinner>
      </div>
    </div>

    <mat-icon
      class="info-link"
      matTooltip="{{infoText}}"
      (mouseenter)="$event.stopImmediatePropagation()"
      (mouseleave)="$event.stopImmediatePropagation()"
      *ngIf="isEntityInfoAllowed(entity)"
      (click)="onInfoClick()">info_outline
    </mat-icon>
  </div>
</td>

<td [ngClass]="{'disabled-cell': isEntityRowDisabled}">
  {{ entity.name }}
</td>

<td style="white-space: nowrap"
    [ngClass]="{'disabled-cell': isEntityRowDisabled}">
  {{ entity.key }}
</td>

<ng-container *ngIf="!isEntitySuspended || entity.isRoot(); else regionals">
  <td [ngClass]="{'disabled-cell': isEntityRowDisabled}">
    <div class="bs-regional">
      <div class="bs-regional__val">
        <a matTooltip="Currency" id="currency" [matMenuTriggerFor]="item"
           [ngClass]="{'disabled-tooltip': disabledCurrencyTooltips}"
           (click)="handleOpenItemsMenu($event)">{{ entity.defaultCurrency }}</a> /
        <a matTooltip="Country" id="country" [matMenuTriggerFor]="item"
           (click)="handleOpenItemsMenu($event)">{{ entity.defaultCountry }}</a> /
        <a matTooltip="Language" id="language" [matMenuTriggerFor]="item"
           (click)="handleOpenItemsMenu($event)">{{ entity.defaultLanguage }}</a>
        <mat-menu class="regional-item" #item>
          <ng-container *ngIf="isAvailableItemsLoaded; else spinner">
            <p *ngFor="let item of availableItems | async" mat-menu-item>{{item.displayName }} ({{item.code}})</p>
          </ng-container>
          <ng-template #spinner>
            <div class="bs-regional__spinner">
              <mat-progress-bar mode="indeterminate"></mat-progress-bar>
            </div>
          </ng-template>
        </mat-menu>
        <button
          mat-icon-button
          class="editRegionBtn"
          [disabled]="entity.isRoot() || (editDisabled$ | async)"
          (click)="handleOpenEditRegional($event)">
          <mat-icon fontSet="material-icons-outline">edit</mat-icon>
        </button>
      </div>
    </div>
  </td>
</ng-container>
<ng-template #regionals>
  <td [ngClass]="{'disabled-cell': isEntityRowDisabled}">
    <div class="bs-regional">
      <div class="bs-regional__val">
        <span matTooltip="Currency">{{ entity.defaultCurrency }}</span> /
        <span matTooltip="Country">{{ entity.defaultCountry }}</span> /
        <span matTooltip="Language">{{ entity.defaultLanguage }}</span>
        <button
          disabled
          mat-icon-button
          class="editRegionBtn">
          <mat-icon fontSet="material-icons-outline">edit</mat-icon>
        </button>
      </div>
    </div>
  </td>
</ng-template>


<td style="text-align: center"
    [ngClass]="{'disabled-cell': isEntitySuspended && !canChangeStatus && !entity.isRoot() }">
  <div class="bs-status">
    <ng-container *ngIf="menuHasItems; else readonlyState">
      <div class="bs-status__chip bs-status__chip--menu sw-chip"
           [class.sw-chip-green]="entity.status === 'normal'"
           [class.sw-chip-default]="entity.status === 'suspended'"
           [class.sw-chip-orange]="entity.status === 'maintenance'"
           [class.sw-chip-red]="entity.status === 'blocked_by_admin'"
           [class.sw-chip-blue]="entity.status === 'test'"
           [matMenuTriggerFor]="statusMenu">
        {{ statusUpdating ? '...' : (entityItemStatusMap[entity.status].title | translate) }}
        <mat-icon class="bs-status__chevron">arrow_drop_down</mat-icon>
      </div>

      <mat-menu #statusMenu="matMenu" xPosition="before" class="bs-status__menu">
        <div
          mat-menu-item
          *ngIf="(entity.status !== 'normal' && canChangeState) || (entity.status === 'test' && canChangeTest)"
          (click)="handleOpenConfirmSetStatus($event, 'normal')">

          <mat-icon class="bs-status__circle sw-color-green">fiber_manual_record</mat-icon>
          {{'BUSINESS_STRUCTURE.WIDGETS.active' | translate}}
        </div>

        <div
          mat-menu-item
          *ngIf="entity.status !== 'suspended' && canChangeState"
          (click)="handleOpenConfirmSetStatus($event, 'suspended')">

          <mat-icon class="bs-status__circle sw-color-grey">fiber_manual_record</mat-icon>
          {{'BUSINESS_STRUCTURE.WIDGETS.inactive' | translate}}
        </div>

        <div
          mat-menu-item
          *ngIf="entity.status !== 'maintenance' && canChangeState"
          (click)="handleOpenConfirmSetStatus($event, 'maintenance')">

          <mat-icon class="bs-status__circle sw-color-orange">fiber_manual_record</mat-icon>
          {{'BUSINESS_STRUCTURE.WIDGETS.maintenance' | translate}}
        </div>

        <div
          mat-menu-item
          *ngIf="entity.status !== 'blocked_by_admin' && isSuperAdmin"
          (click)="handleOpenConfirmSetStatus($event, 'blocked_by_admin')">

          <mat-icon class="bs-status__circle sw-color-deep-orange">fiber_manual_record</mat-icon>
          {{'BUSINESS_STRUCTURE.WIDGETS.blocked' | translate}}
        </div>
      </mat-menu>
    </ng-container>

    <ng-template #readonlyState>
      <div class="bs-status__chip sw-chip"
           [title]="entity.status !== 'blocked_by_admin' ? '' : 'BUSINESS_STRUCTURE.WIDGETS.blockedByAdmin' | translate"
           [class.sw-chip-green]="entity.status === 'normal'"
           [class.sw-chip-default]="entity.status === 'suspended'"
           [class.sw-chip-orange]="entity.status === 'maintenance'"
           [class.sw-chip-red]="entity.status === 'blocked_by_admin'"
           [class.sw-chip-blue]="entity.status === 'test'">
        {{entityItemStatusMap[entity.status].title | translate}}
      </div>
    </ng-template>
  </div>
</td>

<td style="padding-right: 24px; text-align: center; display: flex; justify-content: flex-end;">
  <div>
    <a class="menu-item-link"
       matTooltip="{{'BUSINESS_STRUCTURE.WIDGETS.settings' | translate}}"
       mat-menu-item *ngIf="!entity.isMaster() && !isBrand"
       [disabled]="isEntityRowDisabled"
       [routerLink]="entity.isRoot() ? ['../entities/setup', 'p'] : ['../entities/setup', entity.path, 'p']">
      <mat-icon fontSet="material-icons-outline">settings</mat-icon>
    </a>
  </div>
  <lib-swui-grid-row-actions
    [actions]="rowActions"
    [ignorePlainLink]="true">
  </lib-swui-grid-row-actions>
</td>
