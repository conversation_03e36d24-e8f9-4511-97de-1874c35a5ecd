.name-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.info-link {
  color: black;
  cursor: pointer;
  transition: opacity .25s;
  opacity: .5;
  &:hover {
    opacity: 1;
  }
}

::ng-deep .mat-tooltip {
  white-space: pre-line;
  pointer-events: all;
  max-width: none !important;
}

td {
  border-bottom-color: rgba(0, 0, 0, .12);
  border-bottom-width: 1px;
  border-bottom-style: solid;
  text-align: left;
  padding: 0 8px;
  font-weight: 400;
  font-size: 14px;
  white-space: nowrap;
  height: 48px;

  &:first-child {
    padding-left: 0;

    &:hover {
      .edit-entity {
        display: inline-block;
      }
    }

    .toggle-expand-button {
      padding: 0;
      margin: 0;
      visibility: hidden;
    }

    .expand-visible {
      visibility: visible;
    }
  }
}

.bs-regional {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  cursor: pointer;

  &__val {
    text-transform: uppercase;
    font-weight: 500;
  }

  &:hover {
    .editRegionBtn {
      opacity: 1;
    }
  }

  &__text {
    margin-left: 4px;
  }

  &__spinner {
    width: 200px;
    height: 50px;
    display: flex;
    align-items: center;
    padding: 0 16px;
  }
}

.bs-status {
  &__chip {
    min-width: 120px;
    font-size: 14px;
    justify-content: center;

    &--menu {
      cursor: pointer;
    }
  }

  &__chevron {
    width: 14px;
  }

  &__circle {
    margin-right: 4px;
    vertical-align: middle;
    width: 16px;
    height: 16px;
    font-size: 16px;
    margin-bottom: 2px;
  }
}

.bs-menu {
  &__add {
    position: relative;
    height: 48px;
    padding: 0 16px 0 12px;
    display: flex;
    align-items: center;
    width: 100%;
    margin-bottom: 0 !important;
    color: rgba(0, 0, 0, 0.87);
    cursor: pointer;

    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      display: block;
      width: 100%;
      height: 1px;
      background-color: rgba(0, 0, 0, .12);
    }
  }

  &__icon {
    margin-right: 4px;
  }

  &__dropdown {
    max-height: 240px;
    overflow: auto;
  }

  &__item {
    &.default {
      font-weight: 500;
    }
  }
}

.bs-entity {
  display: flex;
  align-items: center;

  &__arrow {
    position: absolute;
    margin: 10px 0 0 -16px;

    mat-icon {
      cursor: pointer;
    }
  }

  &__type {
    margin: 0 8px;
  }

  &__edit {
    visibility: hidden;
    opacity: 0;
    width: 24px;
    height: 24px;
    margin-bottom: 2px;
    margin-left: 4px;
    color: rgba(0, 0, 0, .54);
    line-height: 24px;
    transition: all 0.15s ease-in-out;

    mat-icon {
      width: 24px;
      height: 24px;
      font-size: 20px;
    }

    &:hover {
      color: rgba(0, 0, 0, .92);
    }
  }

  &:hover {
    .bs-entity__edit {
      visibility: visible;
      opacity: 1;
    }
  }

  &__spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 4px;
    overflow: hidden;
  }
}

.bs-type {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  color: #fff;

  &--entity {
    background-color: #6b67bc;
  }

  &--brand {
    background-color: #bc67a3;
  }

  &--merchant {
    background-color: #67BCAB;
  }

  &--studio {
    background-color: #7CB342;
  }
}

.menu-item-link {
  padding-right: unset;
  text-transform: none;
  color: initial;

  &:hover {
    color: initial;
  }
}

.limits-dialog-body {
  display: block;
  min-width: 400px;
  min-height: 200px;

  max-height: 80vh;
}

.editRegionBtn {
  opacity: 0;

  &:hover {
    opacity: 1;
  }
}

.disabled-cell {
  opacity: 0.5;
}

.disabled-tooltip {
  opacity: 0.5;
  pointer-events: none;
}

::ng-deep .regional-item {
  max-height: 300px;
  overflow-y: auto !important;
  display: flex;
}
