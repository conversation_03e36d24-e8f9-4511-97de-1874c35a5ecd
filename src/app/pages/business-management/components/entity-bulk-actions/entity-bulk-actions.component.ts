import { SelectionModel } from '@angular/cdk/collections';
import { CdkVirtualScrollViewport } from '@angular/cdk/scrolling';
import {
  ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, OnDestroy, OnInit, Renderer2, ViewChild, ViewEncapsulation
} from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatSort } from '@angular/material/sort';
import { MatTable } from '@angular/material/table';
import { ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { PanelAction, SwHubAuthService, SwuiNotificationsService } from '@skywind-group/lib-swui';
import * as moment from 'moment';
import { TableVirtualScrollDataSource } from 'ng-table-virtual-scroll';
import { of, Subject, timer } from 'rxjs';
import { debounceTime, filter, finalize, map, switchMap, take, takeUntil, tap } from 'rxjs/operators';
import { BoConfirmationComponent } from '../../../../common/components/bo-confirmation/bo-confirmation.component';

import { Domain } from '../../../../common/models/domain.model';
import { Entity } from '../../../../common/models/entity.model';
import { CsvSchema, CsvService } from '../../../../common/services/csv.service';
import { EntityService } from '../../../../common/services/entity.service';
import { EntityShortInterface } from '../../../../common/typings';
import { DomainsManagementService } from '../../../domains-management/domains-management.service';
import { SHORT_STRUCTURE_ADDITIONAL } from '../mat-business-structure/business-structure.service';
import { ActionEditComponent } from './action-edit/action-edit.component';
import { BulkEntitySettingsItem, entitiesStructureToBulkOptions } from './structure';
import { SwitchDomainComponent } from './switch-domain/switch-domain.component';

interface EditedDomain {
  domainId: string;
  hasError: boolean;
  newDomainName: string;
  key: string;
  canUndo: boolean;
  skipOnRequest?: boolean;
}

interface EditedDomains {
  static: Record<string, EditedDomain>;
  dynamic: Record<string, EditedDomain>;
}

const csvSchema: CsvSchema[] = [
  {
    name: 'id',
    title: 'Id',
  },
  {
    name: 'key',
    title: 'Key',
  },
  {
    name: 'path',
    title: 'Path',
  },
  {
    name: 'name',
    title: 'Name',
  },
  {
    name: 'fromId',
    title: 'From.domain.id',
  },
  {
    name: 'fromName',
    title: 'From.domain.name',
  },
  {
    name: 'toId',
    title: 'To.domain.id',
  },
  {
    name: 'toName',
    title: 'To.domain.name',
  },
];

@Component({
  selector: 'app-entity-bulk-actions',
  templateUrl: './entity-bulk-actions.component.html',
  styleUrls: ['./entity-bulk-actions.component.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EntityBulkActionsComponent implements OnInit, OnDestroy {

  maxItemsLength: number = 0;
  itemsLength: number = 0;
  items: Record<string, BulkEntitySettingsItem>;
  lockApply = false;
  tabs = ['dynamic', 'static'];
  tab: string = this.tabs[0];
  panelActions: PanelAction[] = [];
  filter = new FormControl('');
  dataSource = new TableVirtualScrollDataSource([]);
  loading = true;
  editedItems: EditedDomains = {
    dynamic: {},
    static: {}
  };
  selections: Record<string, SelectionModel<BulkEntitySettingsItem>> = {
    static: new SelectionModel<BulkEntitySettingsItem>(true, []),
    dynamic: new SelectionModel<BulkEntitySettingsItem>(true, [])
  };
  disabledEnvs = {
    static: '',
    dynamic: ''
  };
  activeIndex = -1;

  @ViewChild(MatSort) set onSort( sort: MatSort ) {
    if (sort) {
      this.dataSource.sort = sort;
      this.dataSource._orderData(this.dataSource.data).forEach((item, index) => item.index = index);
    }
  }

  @ViewChild(MatTable, { read: ElementRef }) table: ElementRef;
  @ViewChild(CdkVirtualScrollViewport) viewPort: CdkVirtualScrollViewport;

  readonly isSuperadmin: boolean;

  private readonly dynamicDomainsMap?: { [id: string]: Domain };
  private readonly staticDomainsMap?: { [id: string]: Domain };
  private readonly dynamicEntityDomain?: Domain;
  private readonly staticEntityDomain?: Domain;
  private readonly dynamicDomains?: Domain[] = [];
  private readonly staticDomains?: Domain[] = [];
  private readonly destroy$ = new Subject<void>();

  get displayedColumns(): string[] {
    return this.tab === 'dynamic'
      ? ['select', 'fullPath', 'text', 'dynamicDomain.environment', 'dynamicDomain.domain']
      : ['select', 'fullPath', 'text', 'staticDomain.domain'];
  }

  constructor( private route: ActivatedRoute,
               private domainsService: DomainsManagementService,
               private entityService: EntityService<Entity>,
               private cdr: ChangeDetectorRef,
               private dialog: MatDialog,
               private renderer: Renderer2,
               private notifications: SwuiNotificationsService,
               private translate: TranslateService,
               private csvService: CsvService,
               auth: SwHubAuthService
  ) {
    const { dynamicEntityDomain, staticEntityDomain, dynamicDomains, staticDomains } = this.route.snapshot.data;
    this.dynamicDomains = dynamicDomains;
    this.staticDomains = staticDomains;
    this.dynamicDomainsMap = dynamicDomains?.reduce(( res, domain ) => ({ ...res, [domain.id]: domain }), {});
    this.staticDomainsMap = staticDomains?.reduce(( res, domain ) => ({ ...res, [domain.id]: domain }), {});
    this.staticEntityDomain = staticEntityDomain;
    this.dynamicEntityDomain = dynamicEntityDomain;
    this.isSuperadmin = auth.isSuperAdmin;

    this.entityService.getShortStructure(SHORT_STRUCTURE_ADDITIONAL.bulkActions, true)
      .pipe(take(1))
      .subscribe(( structure ) => {
        this.items = this.getConvertedStructure(structure);
        this.loading = false;
        this.cdr.markForCheck();
      });

    this.filter.valueChanges
      .pipe(
        tap(() => {
          this.loading = true;
        }),
        debounceTime(300),
        map(value => {
          if (value.length > 2) {
            const items = Object.values(this.items);

            items.forEach(item => {
              if (this.isSearchedItem(item, value)) {
                this.showRelativeItems(item);
              } else {
                item.isVisible = false;
              }
            });

            return items.filter(( { isVisible } ) => isVisible).map((item, index) => ({...item, index}));
          }

          return [];
        }),
        takeUntil(this.destroy$)
      )
      .subscribe(items => {
        this.itemsLength = items.length;

        this.maxItemsLength = items.reduce(( acc, cur ) => {
          if (cur.path.length > acc) {
            return cur.path.length;
          }
          return acc;
        }, 0);

        this.maxItemsLength = (this.maxItemsLength * 7) + 20;

        this.loading = false;
        this.dataSource = new TableVirtualScrollDataSource(items);
        this.cdr.markForCheck();
      });

    this.selections.dynamic.changed
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        if (this.selections.dynamic.selected.length) {
          this.disabledEnvs.dynamic = this.selections.dynamic.selected[0].dynamicDomain.environment;
        } else {
          this.disabledEnvs.dynamic = '';
        }
      });
  }

  trackByFn = ( _: number, item: BulkEntitySettingsItem ) => item.key;

  get isEditSelectedDisabled(): boolean {
    return this.selections[this.tab].isEmpty();
  }

  get isDownloadDisabled(): boolean {
    return !Object.keys(this.editedItems[this.tab]).length;
  }

  get isApplyDisabled(): boolean {
    return !Object.values(this.editedItems).reduce(( res, item ) => {
      if (!res) {
        return Object.keys(item).length;
      }

      return res;
    }, false);
  }

  ngOnInit() {
    this.setPanelActions();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onSelect( { value: tab } ): void {
    this.filter.setValue('');
    if (tab === 'static') {
      this.selections?.dynamic.clear();
      this.editedItems.dynamic = {};
    } else {
      this.selections?.static.clear();
      this.editedItems.static = {};
    }
    this.tab = tab;
  }

  onLinkClick( name: string ) {
    this.activeIndex = this.dataSource._orderData(this.dataSource.data).findIndex(item => item.text === name);
    let el: HTMLElement | null;

    this.viewPort.scrollToIndex(this.activeIndex);
    timer(10)
      .pipe(take(1))
      .subscribe(() => {
        el = document.getElementById(name);

        this.renderer.addClass(el, 'found');
      });

    timer(510)
      .pipe(take(1))
      .subscribe(() => {
        this.renderer.removeClass(el, 'found');
      });
  }

  onItemEditClick( item: BulkEntitySettingsItem ) {
    const componentName = `${this.tab}Domain`;
    const itemDomain = item[componentName];
    const editedDomain = this.editedItems[this.tab][item.key];

    const domain = editedDomain && this[`${componentName}sMap`][editedDomain.domainId] || itemDomain;
    const domains = `${componentName}s` === 'dynamicDomains' ? this.dynamicDomains : this.staticDomains;
    this.dialog.open(ActionEditComponent, {
      data: {
        item,
        componentName: componentName,
        domains: domains,
        domain
      },
      disableClose: true
    }).afterClosed()
      .pipe(
        take(1),
        filter(data => !!data),
      )
      .subscribe(( domainId: string ) => {
        this.updateItemWithChildren(item.key, domainId, true);
        this.cdr.markForCheck();
      });
  }

  onSelectedItemsEditClick() {
    const componentName = `${this.tab}Domain`;

    const domains = `${componentName}s` === 'dynamicDomains' ? this.dynamicDomains : this.staticDomains;
    this.dialog.open(ActionEditComponent, {
      data: {
        componentName: componentName,
        domains: domains,
        multiple: true,
        domain: {
          environment: this.disabledEnvs[this.tab]
        }
      },
      disableClose: true
    }).afterClosed()
      .pipe(
        take(1),
        filter(data => !!data),
      )
      .subscribe(( domainId: string ) => {
        this.selections[this.tab].selected.forEach(item => {
          this.updateItemWithChildren(item.key, domainId, true);
        });
        this.cdr.markForCheck();
      });
  }

  switchDomainClick() {
    const componentName = `${this.tab}Domain`;

    const domains = `${componentName}s` === 'dynamicDomains' ? this.dynamicDomains : this.staticDomains;
    const confirm$ = this.isApplyDisabled
      ? of(true)
      : this.dialog.open(BoConfirmationComponent,
        {
          data: { message: 'BULK_ACTIONS.switchAllConfirm' },
          disableClose: true
        }).afterClosed();

    confirm$
      .pipe(
        take(1),
        filter(data => !!data),
        switchMap(() => {
          return this.dialog.open(SwitchDomainComponent, {
            data: {
              componentName: componentName,
              domains: domains,
              type: this.tab
            },
            disableClose: true
          }).afterClosed();
        }),
        filter(data => !!data)
      )
      .subscribe(( { from, to } ) => {
        this.selections[this.tab].clear();
        this.editedItems[this.tab] = {};
        this.showByDomain(from);

        const items = Object.values(this.items);

        items.filter(item => {
          const domain = item[componentName];
          return !domain.inherited && domain.id === from;
        }).forEach(item => {
          this.updateItemWithChildren(item.key, to, true);
        });
      });
  }

  applyBulkOperations() {
    const canApply = this.getApplyState();

    if (!canApply) {
      return;
    }

    const dynamicItems = Object.values(this.editedItems.dynamic)
      .filter(( { skipOnRequest } ) => !skipOnRequest)
      .map(( item: any ) => {
        return this.getDomainFromValue(item.domainId, item.key, 'dynamic');
      });
    const staticItems = Object.values(this.editedItems.static)
      .filter(( { skipOnRequest } ) => !skipOnRequest)
      .map(( item: any ) => {
        return this.getDomainFromValue(item.domainId, item.key, 'static');
      });

    const bulkRequestData = [...dynamicItems, ...staticItems];

    this.lockApply = true;
    this.loading = true;
    this.domainsService.bulkOperation(bulkRequestData)
      .pipe(
        switchMap(() => this.entityService.getShortStructure(SHORT_STRUCTURE_ADDITIONAL.bulkActions, true)),
        tap(() => this.notifications.success(this.translate.instant('BULK_ACTIONS.notificationUpdate'))),
        finalize(() => {
          this.lockApply = false;
          this.loading = false;
          this.cdr.markForCheck();
        }),
        takeUntil(this.destroy$)
      )
      .subscribe(structure => {
        this.items = this.getConvertedStructure(structure);
        this.editedItems = {
          dynamic: {},
          static: {}
        };
        this.selections?.static.clear();
        this.selections?.dynamic.clear();
        this.loading = false;
        this.filter.updateValueAndValidity();
        this.cdr.detectChanges();
      });

  }

  getDomainFromValue( value, entityKey, type ) {
    const action = value === 'inherited' ? 'reset' : 'set';
    let item: { type: string, id?: string } = { type };

    if (action === 'set') {
      item.id = value;
    }

    return {
      action,
      item,
      entityKey
    };
  }

  undo( domain: string, key: string, force?: boolean ) {
    const item = this.items[key];
    const undoItem = this.editedItems[domain][key];

    if (!undoItem || undoItem.canUndo && !force) {
      return;
    }

    const editedParentItemDomain = this.editedItems[domain][item.parentKey];

    if (item[`${domain}Domain`].inherited && editedParentItemDomain) {
      this.editedItems[domain][key] = {
        ...editedParentItemDomain,
        key,
        canUndo: false
      };
    } else {
      delete this.editedItems[domain][key];
    }

    item.childrenIds.forEach(itemKey => {
      this.undo(domain, itemKey);
    });
  }

  updateItemWithChildren( key: string, domainId: string, force?: boolean ) {
    const item = this.items[key];
    const itemDomain = item[`${this.tab}Domain`];
    const parentItemDomain = item.parentKey ? this.items[item.parentKey][`${this.tab}Domain`] : {};

    if (!itemDomain.inherited && !force) {
      return;
    }

    const domainsMap = this[`${this.tab}DomainsMap`];
    let domainData;

    if (!force) {
      domainData = {
        ...this.editedItems[this.tab][item.parentKey],
        skipOnRequest: true
      };
    } else {
      const newItemDomain = domainId === 'inherited' ? parentItemDomain : domainsMap[domainId];

      let newDomainName;

      if (this.tab === 'dynamic') {
        newDomainName = `${newItemDomain.environment} - ${newItemDomain.domain}`;
      } else {
        newDomainName = newItemDomain.domain;
      }

      domainData = {
        domainId,
        hasError: newItemDomain.environment !== itemDomain.environment,
        newDomainName
      };
    }

    this.editedItems[this.tab][key] = {
      ...domainData,
      key,
      canUndo: !!force
    };

    item.childrenIds.forEach(itemKey => {
      this.updateItemWithChildren(itemKey, domainId);
    });
  }

  downloadCsv() {
    const domainTypeTitle = this.tab[0].toUpperCase() + this.tab.slice(1);
    const fileName = `${domainTypeTitle} domains changes ${moment().format('YYYY-MM-DD HH:MM')}`;
    const domainName = `${this.tab}Domain`;
    const changes = Object.entries(this.editedItems[this.tab]).map(( [key, domain]: [string, any] ) => {
      const item = this.items[key];
      const oldDomain = item[domainName];
      const { id, path, text } = item;

      return {
        key, id, path, name: text,
        fromId: oldDomain.id,
        fromName: `${oldDomain.environment} - ${oldDomain.domain}`,
        toId: domain.domainId,
        toName: domain.newDomainName
      };
    });
    this.csvService.exportToCsv(csvSchema, changes, fileName, [
      'id', 'key', 'path', 'name', 'fromId', 'fromName', 'toId', 'toName',
    ]);
  }

  showByDomain( domain?: string ) {
    this.filter.setValue('', { emitEvent: false });
    let items = Object.values(this.items);

    if (domain) {
      const domainName = `${this.tab}Domain`;

      items.forEach(item => {
        if (item[domainName]?.id === domain) {
          this.showRelativeItems(item);
        } else {
          item.isVisible = false;
        }
      });

      items = items.filter(( { isVisible } ) => isVisible);
    }

    this.itemsLength = items.length;

    this.maxItemsLength = items.reduce(( acc, cur ) => {
      if (cur.path.length > acc) {
        return cur.path.length;
      }
      return acc;
    }, 0);

    this.maxItemsLength = (this.maxItemsLength * 7) + 20;

    this.loading = false;
    this.dataSource = new TableVirtualScrollDataSource(items);
    this.cdr.markForCheck();
  }

  private getApplyState() {
    if (Object.keys(this.editedItems.dynamic).length || Object.keys(this.editedItems.static).length) {
      const hasErrors = Object.values(this.editedItems.dynamic)
        .some(( { hasError } ) => hasError);

      return !hasErrors;
    }

    return false;
  }

  private showRelativeItems( item: BulkEntitySettingsItem, showParents = true, showChildren = true ): void {
    item.isVisible = true;

    if (showChildren) {
      item.childrenIds.forEach(id => {
        this.showRelativeItems(this.items[id], false);
      });
    }

    if (item.parentKey && showParents) {
      this.showRelativeItems(this.items[item.parentKey], true, false);
    }
  }

  private getConvertedStructure( structure: EntityShortInterface ): Record<string, BulkEntitySettingsItem> {
    return entitiesStructureToBulkOptions({
      item: structure,
      dynamicDomainId: this.dynamicEntityDomain?.id,
      staticDomainId: this.staticEntityDomain?.id,
      dynamicDomainsMap: this.dynamicDomainsMap,
      staticDomainsMap: this.staticDomainsMap,
      resultObject: {}
    });
  }

  private isSearchedItem( item: BulkEntitySettingsItem, value: string ): boolean {
    const lowerValue = value.toLowerCase();

    const domainName = `${this.tab}Domain`;
    const domainData = this[`${[`${domainName}sMap`]}`][item[domainName].id];
    const domain = domainData.domain.toLowerCase();
    const environment = (domainData.environment || '').toLowerCase();

    const hasDomain = domain.includes(lowerValue) || environment.includes(lowerValue);

    return hasDomain || item.text.toLowerCase().includes(lowerValue);
  }

  private setPanelActions() {
    this.panelActions.push(
      {
        title: 'Switch domain',
        actionFn: () => this.switchDomainClick(),
        getStyle: () => {
          return { color: '#1373d5' };
        }
      },
      {
        title: 'Edit selected',
        color: 'primary',
        actionFn: () => this.onSelectedItemsEditClick(),
        disabledFn: () => this.isEditSelectedDisabled
      },
      {
        title: 'ALL.confirm',
        color: 'primary',
        actionFn: () => this.applyBulkOperations(),
        disabledFn: () => this.isApplyDisabled
      }
    );
  }
}
