<div class="tables-row__header">
  <div class="tables-row__title">{{ 'ENTITY_SETUP.REGIONAL.titleCurrencies' | translate }}</div>
  <button mat-icon-button (click)="showCurrencyModal()" matTooltip="{{ 'ENTITY_SETUP.REGIONAL.manage' | translate }}" *ngIf="allowedEdit">
    <mat-icon>tune</mat-icon>
  </button>
</div>

<mat-table [dataSource]="dataSource" class="mat-table--simple" >
  <ng-container matColumnDef="displayName">
    <mat-header-cell *matHeaderCellDef>{{ 'ENTITY_SETUP.REGIONAL.currencyName' | translate }}</mat-header-cell>
    <mat-cell *matCellDef="let currency">
      <span [ngClass]="{'text-bold': currency.isDefault }">
        {{ currency.displayName + ' (' + currency.label + ')' }}
      </span>
    </mat-cell>
  </ng-container>

  <ng-container matColumnDef="balance">
    <mat-header-cell *matHeaderCellDef>{{ 'ENTITY_SETUP.REGIONAL.balance' | translate }}</mat-header-cell>
    <mat-cell *matCellDef="let currency">
      <a href="#" *ngIf="allowedToChangeBalance; else readOnlyBalance" (click)="showBalanceModal($event, currency.code)">
        <u>{{ currency['balance'] | formattedMoney: 2: ' ': currency.code }}</u>
      </a>
      <ng-template #readOnlyBalance>
        {{ currency['balance'] | formattedMoney: 2: ' ': currency.code }}
      </ng-template>
    </mat-cell>
  </ng-container>

  <ng-container matColumnDef="code">
    <mat-header-cell *matHeaderCellDef class="mat-table align-right">
      <input matInput trimValue
             class="search-field"
             autocomplete="off"
             [formControl]="searchControl"
             placeholder="{{'ENTITY_SETUP.GAMES.searchPlaceholder' | translate}}">
    </mat-header-cell>
    <mat-cell *matCellDef="let currency" class="align-right">
      <ng-container *ngIf="currency['isDefault']; else actionsMenu">
        <span class="sw-chip"> {{ 'ENTITY_SETUP.REGIONAL.default' | translate }}</span>
      </ng-container>
      <ng-template #actionsMenu>
        <lib-swui-grid-row-actions [actions]="rowActions" [row]="currency" menuIcon="menu"></lib-swui-grid-row-actions>
      </ng-template>
    </mat-cell>
  </ng-container>

  <mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></mat-header-row>
  <mat-row *matRowDef="let currency; columns: displayedColumns;"></mat-row>
</mat-table>
