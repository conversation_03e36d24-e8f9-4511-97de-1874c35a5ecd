import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { SwitcheryModule } from '@skywind-group/lib-swui';
import { BoConfirmationModule } from '../../../../../common/components/bo-confirmation/bo-confirmation.module';
import { ControlMessagesModule } from '../../../../../common/components/control-messages/control-messages.module';
import { EntityDomainsComponent } from './entity-domains/entity-domains.component';
import { EntityMaintenanceComponent } from './entity-maintenance/entity-maintenance.component';
import { ManageDomainsModule } from './manage-domains/manage-domains.module';
import { ManageMerchantParamsModule } from './manage-merchant-params/manage-merchant-params.module';
import { TabDomainsComponent } from './tab-domains.component';
import { TabDomainsRoutingModule } from './tab-domains.routing';

@NgModule({
  imports: [
    CommonModule,
    TabDomainsRoutingModule,
    TranslateModule.forChild(),
    ReactiveFormsModule,
    ControlMessagesModule,
    SwitcheryModule,
    ManageDomainsModule,
    ManageMerchantParamsModule,
    BoConfirmationModule,

    MatDividerModule,
    MatFormFieldModule,
    MatButtonModule,
    MatInputModule,
    MatSlideToggleModule,
    MatIconModule,
    MatTooltipModule,
    MatCardModule,
  ],
  declarations: [
    TabDomainsComponent,
    EntityDomainsComponent,
    EntityMaintenanceComponent,
  ],
})
export class TabDomainsModule {
}
