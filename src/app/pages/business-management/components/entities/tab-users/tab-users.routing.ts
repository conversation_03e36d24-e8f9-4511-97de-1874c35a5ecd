import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { StructureResolver } from '../../../../../common/services/resolvers/structure.resolver';

import { TabUsersComponent } from './tab-users.component';
import { BriefResolver } from '../../../../../common/services/resolvers/brief.resolver';


@NgModule({
  imports: [
    RouterModule.forChild([
      {
        path: '',
        component: TabUsersComponent,
        resolve: {
          structure: StructureResolver,
          brief: BriefResolver,
        }
      }
    ])
  ],
  exports: [
    RouterModule,
  ]
})
export class TabUsersRoutingModule {

}
