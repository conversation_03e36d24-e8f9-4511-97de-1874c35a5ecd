import { SwuiSelectOption } from '@skywind-group/lib-swui';
import minify from 'css-minify';
import { isEqual } from 'lodash';
import { OPTIONS, THEMES } from './theme.model';
import { environment } from '../../../environments/environment';

export const AVAILABLE_LANGUAGES: SwuiSelectOption[] = [
  { id: 'en', text: 'LANGUAGES.en' },
  { id: 'zh', text: 'LANGUAGES.zh' },
  { id: 'zh-tw', text: 'LANGUAGES.zh-tw' },
  { id: 'ja', text: 'LANGUAGES.ja' },
  { id: 'ko', text: 'LANGUAGES.ko' },
  { id: 'ms', text: 'LANGUAGES.ms' },
  { id: 'id', text: 'LANGUAGES.id' },
  { id: 'vi', text: 'LANGUAGES.vi' },
  { id: 'th', text: 'LANGUAGES.th' },
  { id: 'ro', text: 'LANGUAGES.ro' },
  { id: 'it', text: 'LANGUAGES.it' },
  { id: 'el', text: 'LANGUAGES.el' },
  { id: 'km', text: 'LANGUAGES.km' },
  { id: 'es', text: 'LANGUAGES.es' },
  { id: 'pt', text: 'LANGUAGES.pt' },
  { id: 'pt-br', text: 'LANGUAGES.pt-br' },
  { id: 'ru', text: 'LANGUAGES.ru' },
  { id: 'de', text: 'LANGUAGES.de' },
  { id: 'sv', text: 'LANGUAGES.sv' },
  { id: 'da', text: 'LANGUAGES.da' },
  { id: 'nl', text: 'LANGUAGES.nl' },
  { id: 'bg', text: 'LANGUAGES.bg' },
  { id: 'sr', text: 'LANGUAGES.sr' },
  { id: 'tr', text: 'LANGUAGES.tr' },
];

export interface LobbyInfoOption {
  key: string;
  value: any;
}

export interface LobbyThemeOption extends LobbyInfoOption {
  css?: string;
  meta?: string;
  token?: string;
}

export interface LobbyTheme {
  key: string;
  options: LobbyThemeOption[];
}

export interface LobbyMenuItemTranslations {
  [key: string]: {
    title: string;
    description?: string;
    icon?: any;
  };
}

export type LobbyMenuItemWidgetOptionsPositionFixed = 'left' | 'right' | 'center';

export interface LobbyMenuItemWidgetGridOptionsPositions {
  width?: number;
  height?: number;
  fixed?: LobbyMenuItemWidgetOptionsPositionFixed | string;
  x?: number;
  y?: number;
}


export interface LobbyMenuItemWidgetOptions {
  desktop?: LobbyMenuItemWidgetGridOptionsPositions;
  mobile?: LobbyMenuItemWidgetGridOptionsPositions;
  portrait?: LobbyMenuItemWidgetGridOptionsPositions;

  [prop: string]: any;
}

export interface LobbyMenuItemWidget {
  order?: number;
  tag: string;
  src: string;
  path: string;
  options?: LobbyMenuItemWidgetOptions;
}

export interface LobbyMenuItemWidgets {
  [key: string]: LobbyMenuItemWidget;
}

export interface LobbyMenuItemOptions {
  isCommissionFilter?: boolean;
  isGridLayout?: boolean;
  widgets?: LobbyMenuItemWidgets;
}

export interface LobbyMenuItemRibbon {
  text: string;
  bg: string;
  color: string;
}

export interface LobbyMenuItemTranslation {
  title: string;
  description?: string;
  icon?: string;
  ribbon?: string | LobbyMenuItemRibbon;
}

export interface LobbyMenuItemSettings extends LobbyMenuItemTranslation {
  slug?: string;
  translations?: {
    [lang: string]: LobbyMenuItemTranslation;
  };
}

export interface LobbyMenuItemGameRibbon {
  [code: string]: string | LobbyMenuItemRibbon;
}

export interface LobbyMenuItemGameOverlay {
  [code: string]: string;
}

export interface LobbyMenuItemGameRibbonTranslations {
  [lang: string]: LobbyMenuItemGameRibbon;
}

export interface LobbyMenuItemGameOverlayTranslations {
  [lang: string]: LobbyMenuItemGameOverlay;
}

export interface LobbyMenuItem extends LobbyMenuItemSettings {
  gameCategoryId?: string;
  widget?: LobbyMenuItemWidget;
  showFavoriteGames?: boolean;
  showRecentGames?: boolean;
  subcategories?: LobbyMenuItem[];
  options?: LobbyMenuItemOptions;
  gameRibbons?: LobbyMenuItemGameRibbonTranslations;
  overlayUrls?: LobbyMenuItemGameOverlayTranslations;
}

interface LobbyInfo {
  theme?: {
    key: string;
    version?: string;
  };
  options?: LobbyInfoOption[];
  menuItems?: LobbyMenuItem[];
  requiredBuildAt?: string;
  widgets?: LobbyMenuItemWidgets;
}

export interface UpdateLobbyData {
  title: string;
  description?: string | null;
  theme?: LobbyTheme;
  info?: LobbyInfo;
}

export interface LobbyLinks {
  web?: string;
}

export interface LobbyBuildState {
  isReady: boolean;
  isBuilding: boolean;
  isUpdated: boolean;
  links?: LobbyLinks;
}

export interface LobbyShortData {
  id: string;
  key: string;
  title: string;
  description?: string | null;
  status: 'normal' | 'suspended';
  info?: LobbyInfo;
  createdAt: string;
  updatedAt: string;
  build?: LobbyBuildState;
}

export interface LobbyExtendedData extends LobbyShortData {
  theme?: LobbyTheme;
}

export interface LobbyBuild {
  updatedAt?: number;
  pwa?: {
    version?: number;
    outdated: boolean;
    url: string;
  };
  build?: {
    id: number | string;
    status: 'completed' | 'waiting' | 'active' | 'delayed' | 'failed';
  }[];
}

export interface LobbiesBuild {
  [key: string]: LobbyBuild;
}

export function isParentMenuItem( item: LobbyMenuItem | undefined ): boolean {
  if (item) {
    if (item.gameCategoryId) {
      return false;
    }
    if (item.widget) {
      return false;
    }
    if (item.showRecentGames) {
      return false;
    }
    return !item.showFavoriteGames;
  }
  return false;
}

export function buildRequired( lobby: LobbyExtendedData, data: UpdateLobbyData ): boolean {
  if (lobby.title !== data.title) {
    console.log('Build required. Title mismatch.');
    return true;
  }
  if (lobby.theme.key !== data.theme.key) {
    console.log('Build required. Theme mismatch.');
    return true;
  }
  if (lobby.info.theme.version !== environment.APP_VERSION) {
    console.log('Build required. Theme has new version.');
    return true;
  }

  const options = { ...(THEMES[lobby.theme.key].options), ...OPTIONS };

  function getOptions( lobbyThemeOptions: LobbyThemeOption[] ): { [key: string]: any } {
    return lobbyThemeOptions.filter(( { key } ) => key in options).reduce(( result, { key, value } ) => ({
      ...result,
      [key]: value
    }), {});
  }

  const lobbyOptions = getOptions(lobby.theme.options);
  const dataOptions = getOptions(data.theme.options);
  const buildTargets = ['css', 'token', 'meta', 'builder'];
  return Object.entries(lobbyOptions).filter(( [key] ) => {
    const { target } = options[key];
    if (Array.isArray(target)) {
      return target.some(item => buildTargets.includes(item));
    }
    return buildTargets.includes(target);
  }).some(( [key, value] ) => {
    const mismatch = !isEqual(value, dataOptions[key]);
    if (mismatch) {
      console.log(`Build required. Value of [${key}] mismatch.`);
    }
    return mismatch;
  });
}

export function process( builds: LobbiesBuild, lobby: LobbyShortData ): LobbyShortData {
  return {
    ...lobby,
    build: buildState(builds[lobby.key])
  };
}

function buildState( build?: LobbyBuild ): LobbyBuildState {
  if (!build) {
    return {
      isReady: false,
      isBuilding: false,
      isUpdated: true
    };
  }
  const state = {
    isReady: false,
    isBuilding: false,
    isUpdated: false
  };
  state.isBuilding = (build.build || [])
    .filter(( { status } ) => status === 'waiting' || status === 'active' || status === 'delayed')
    .length > 0;
  if (!state.isBuilding) {
    state.isReady = !!build.pwa;
    state.isUpdated = build.pwa?.outdated || false;
  }
  return {
    ...state,
    links: {
      ...(build.pwa ? { web: build.pwa.url } : {}),
    }
  };
}

export function upgradeLobbyThemeOptions( options: LobbyThemeOption[] ): LobbyThemeOption[] {
  return upgradePreloaderOption(upgradeGameLaunchModeOption(options));
}

function getThemeOptionValue( options: LobbyThemeOption[], name: string ): any {
  const option = options.find(( { key } ) => key === name);
  return option ? option.value : undefined;
}

function upgradeGameLaunchModeOption( options: LobbyThemeOption[] ): LobbyThemeOption[] {
  const gameLaunchMode = {
    key: 'gameLaunchMode',
    value: {
      pwaDesktop: 'modal',
      pwaMobile: 'modal',
      ...getThemeOptionValue(options, 'gameLaunchMode')
    }
  };
  return [
    ...options.filter(( { key } ) => !['pwaDesktopGameLaunchMode', 'pwaMobileGameLaunchMode', 'gameLaunchMode'].includes(key)),
    gameLaunchMode,
  ];
}

function upgradePreloaderOption( options: LobbyThemeOption[] ): LobbyThemeOption[] {
  const preloader = getThemeOptionValue(options, 'preloader') || {
    key: 'preloader',
    value: {
      showPreloader: getThemeOptionValue(options, 'showPreloader') || false,
      lobbyPreloaderLogo: getThemeOptionValue(options, 'lobbyPreloaderLogo'),
      lobbyPreloaderAnimation: getThemeOptionValue(options, 'lobbyPreloaderAnimation')
    }
  };
  return [
    ...options.filter(( { key } ) => !['showPreloader', 'lobbyPreloaderLogo', 'lobbyPreloaderAnimation'].includes(key)),
    preloader,
  ];
}

export function buildOptions( themeKey: string, options: LobbyThemeOption[] ): { theme: LobbyThemeOption[]; info: LobbyInfoOption[] } {
  const props = { ...THEMES[themeKey].options, ...OPTIONS };
  const info: { [key: string]: LobbyInfoOption } = {};
  const theme: { [key: string]: LobbyThemeOption } = {};
  options.forEach(( { key, value } ) => {
    if (key in props) {
      const { cssFn, metaFn, target } = props[key];
      const targets = Array.isArray(target) ? target : [target];
      if (targets.includes('config')) {
        info[key] = { key, value };
      }
      theme[key] = {
        key,
        value,
        ...(cssFn ? { css: minify(cssFn(value)) } : {}),
        ...(metaFn ? { meta: metaFn(value) } : {}),
        ...(targets.includes('token') ? { token: value } : {})
      };
    }
  });
  return { theme: Object.values(theme), info: Object.values(info) };
}

export function rebuildOptions( { title, description, info, theme }: LobbyExtendedData ): UpdateLobbyData {
  const options = [...(info.options || []), ...upgradeLobbyThemeOptions(theme.options || [])];
  const { info: infoOptions, theme: themeOptions } = buildOptions(theme.key, options);
  return {
    title,
    description: description || null,
    theme: {
      ...theme,
      options: themeOptions
    },
    info: {
      ...info,
      options: infoOptions
    }
  };
}
