import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { LobbyWidget } from '../../../../../../common/services/lobby-widgets.service';
import { LobbyMenuItemOptions } from '../../../../lobby.model';

export interface LobbyWidgets {
  [tag: string]: LobbyWidget;
}

export interface WidgetOptions {
  [prop: string]: any;
}

@Component({
  selector: 'lobby-menu-items-options',
  templateUrl: './lobby-menu-items-options.component.html',
  styleUrls: ['./lobby-menu-items-options.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LobbyMenuItemsOptionsComponent implements OnInit, OnDestroy {
  readonly form: FormGroup;

  @Input('submitted')
  set setSubmitted( val: boolean | undefined ) {
    if (val) {
      this.form.markAllAsTouched();
    }
  }

  @Input('options')
  set setOptions( val: LobbyMenuItemOptions | undefined ) {
    this.form.patchValue({
      isCommissionFilter: val?.isCommissionFilter ?? false,
      isGridLayout: val?.isGridLayout ?? false
    }, { emitEvent: false });
  }

  @Output() optionsChanged = new EventEmitter<LobbyMenuItemOptions>();
  @Output() validStatusChange = new EventEmitter<boolean>();

  private readonly destroyed$ = new Subject<void>();

  constructor() {
    this.form = new FormGroup({
      isCommissionFilter: new FormControl(false),
      isGridLayout: new FormControl(false)
    });
  }

  ngOnInit(): void {
    this.form.valueChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(val => {
      if (this.form.valid) {
        this.optionsChanged.emit(val);
      }
    });

    this.form.statusChanges.pipe(
      takeUntil(this.destroyed$)
    ).subscribe(result => {
      this.validStatusChange.emit(result === 'VALID');
    });
  }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }
}
